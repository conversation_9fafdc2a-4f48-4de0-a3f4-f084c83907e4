import { Injectable } from '@nestjs/common';
import { Model, Document, FilterQuery } from 'mongoose';
import {
  PaginationOptionsDto,
  PaginationResultDto,
} from '../dtos/pagination.dto';

@Injectable()
export class PaginationService {
  async paginate<T extends Document>(
    model: Model<T>,
    query: FilterQuery<T>,
    options: PaginationOptionsDto,
  ): Promise<PaginationResultDto<T>> {
    const { limit, page, sort } =
      PaginationService.formatPaginationOptions(options);

    const total = await model.countDocuments(query).exec();
    let data;

    if (model.modelName === 'Comment') {
      data = await model
        .find(query)
        .populate('platformUser')
        .sort(sort)
        .limit(limit)
        .skip(limit * (page - 1))
        .exec();
    } else {
      data = await model
        .find(query)
        .sort(sort)
        .limit(limit)
        .skip(limit * (page - 1))
        .exec();
    }

    const pages = Math.ceil(total / limit);
    const meta = { total, page, limit, pages };

    return new PaginationResultDto<T>(data, meta);
  }

  static formatPaginationOptions(
    options: PaginationOptionsDto,
  ): PaginationOptionsDto {
    return {
      limit: options.limit > 0 ? options.limit : 10,
      page: options.page > 0 ? options.page : 1,
      sort: options.sort || '-createdAt',
    };
  }
}
