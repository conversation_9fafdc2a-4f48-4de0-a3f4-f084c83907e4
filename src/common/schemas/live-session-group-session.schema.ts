import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { LiveSessionGroupDocument } from '@common/schemas/live-session-group.schema';
import { LiveSessionDocument } from '@common/schemas/live-session.schema';

@Schema({ timestamps: true })
export class LiveSessionGroupSession {
  @Prop({ type: String, ref: 'LiveSessionGroup', required: true, index: true })
  liveSessionGroupId: string;

  @Prop({ type: String, ref: 'LiveSession', required: true, index: true })
  liveSessionId: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export type LiveSessionGroupSessionDocument = LiveSessionGroupSession &
  Document & {
    liveSessionGroup?: LiveSessionGroupDocument;
    liveSession?: LiveSessionDocument;
  };

export const LiveSessionGroupSessionSchema = SchemaFactory.createForClass(
  LiveSessionGroupSession,
);

// Create compound index for uniqueness and performance
LiveSessionGroupSessionSchema.index(
  {
    liveSessionGroupId: 1,
    liveSessionId: 1,
  },
  { unique: true },
);

LiveSessionGroupSessionSchema.virtual('liveSessionGroup', {
  ref: 'LiveSessionGroup',
  localField: 'liveSessionGroupId',
  foreignField: 'id',
  justOne: true,
});

LiveSessionGroupSessionSchema.virtual('liveSession', {
  ref: 'LiveSession',
  localField: 'liveSessionId',
  foreignField: 'id',
  justOne: true,
});

LiveSessionGroupSessionSchema.set('toJSON', { virtuals: true });
LiveSessionGroupSessionSchema.set('toObject', { virtuals: true });
