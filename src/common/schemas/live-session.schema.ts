import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { LivestreamPlatform } from '@common/constants';
import { SystemUserDocument } from '@common/schemas/system-user.schema';
import { PlatformChannelDocument } from '@common/schemas/platform-channel.schema';
import { LiveSessionGroupDocument } from '@common/schemas/live-session-group.schema';

export type LiveSessionDocument = LiveSession &
  Document & {
    user: SystemUserDocument;
    platformChannel: PlatformChannelDocument;
    liveSessionGroups?: LiveSessionGroupDocument[];
  };

export type LiveSessionHistoryDocument = HydratedDocument<LiveSessionHistory>;

export class LiveSessionHistory {
  @Prop({ type: String, index: true })
  sessionId: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

@Schema()
export class LiveSession {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  roomId: string;

  @Prop({ type: String, index: true })
  deviceId: string;

  @Prop({ type: String })
  roomTitle: string;

  @Prop({ type: String })
  shareLink: string;

  @Prop({ type: String, index: true })
  username: string;

  @Prop({ type: String, enum: LivestreamPlatform })
  livestreamPlatform: LivestreamPlatform;

  @Prop({
    type: String,
    index: true,
  })
  userId: string;

  @Prop({
    type: String,
    index: true,
  })
  platformChannelId: string;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'LiveSessionHistory', default: [] }],
  })
  histories: LiveSessionHistory[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export const LiveSessionSchema = SchemaFactory.createForClass(LiveSession);

LiveSessionSchema.virtual('user', {
  ref: 'SystemUser',
  localField: 'userId',
  foreignField: 'id',
  justOne: true,
});

LiveSessionSchema.virtual('platformChannel', {
  ref: 'PlatformChannel',
  localField: 'platformChannelId',
  foreignField: 'id',
  justOne: true,
});

LiveSessionSchema.virtual('liveSessionGroups', {
  ref: 'LiveSessionGroupSession',
  localField: 'id',
  foreignField: 'liveSessionId',
  justOne: false,
});

LiveSessionSchema.set('toJSON', { virtuals: true });
LiveSessionSchema.set('toObject', { virtuals: true });
