import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { LiveSessionSchema } from '@common/schemas/live-session.schema';

export type LiveSessionGroupDocument = LiveSessionGroup & Document;

@Schema()
export class LiveSessionGroup {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  createdBy: string;

  @Prop({ type: String })
  name: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export const LiveSessionGroupSchema =
  SchemaFactory.createForClass(LiveSessionGroup);

LiveSessionSchema.virtual('creator', {
  ref: 'SystemUser',
  localField: 'createdBy',
  foreignField: 'id',
  justOne: true,
});
