import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { SystemUserDocument } from '@common/schemas/system-user.schema';
import { LiveSessionDocument } from '@common/schemas/live-session.schema';

export type LiveSessionGroupDocument = LiveSessionGroup &
  Document & {
    creator?: SystemUserDocument;
    liveSessions?: LiveSessionDocument[];
  };

@Schema()
export class LiveSessionGroup {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  createdBy: string;

  @Prop({ type: String })
  name: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export const LiveSessionGroupSchema =
  SchemaFactory.createForClass(LiveSessionGroup);

LiveSessionGroupSchema.virtual('creator', {
  ref: 'SystemUser',
  localField: 'createdBy',
  foreignField: 'id',
  justOne: true,
});

LiveSessionGroupSchema.virtual('liveSessions', {
  ref: 'LiveSession',
  localField: 'id',
  foreignField: 'liveSessionGroupId',
  justOne: false,
});

LiveSessionGroupSchema.set('toJSON', { virtuals: true });
LiveSessionGroupSchema.set('toObject', { virtuals: true });
