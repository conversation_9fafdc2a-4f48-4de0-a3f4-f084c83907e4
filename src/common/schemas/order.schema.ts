import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { PlatformUser } from './platform-user.schema';
import { LiveSessionDocument } from '@common/schemas/live-session.schema';
import { CommentDocument } from '@common/schemas/comment.schema';
import { PlatformChannelDocument } from '@common/schemas/platform-channel.schema';
import { LiveSessionGroupDocument } from '@common/schemas/live-session-group.schema';

export type OrderDocument = Order &
  Document & {
    comment?: CommentDocument;
    liveSession?: LiveSessionDocument;
    liveSessionGroup?: LiveSessionGroupDocument;
    platformUser?: PlatformUser;
    platformChannel?: PlatformChannelDocument;
  };

@Schema()
export class Order {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, required: true })
  orderCode: string;

  @Prop({ type: String, required: true, index: true })
  createdById: string;

  @Prop({ type: String, index: true })
  platformChannelId: string;

  @Prop({ type: String, index: true })
  platformUserId: string;

  @Prop({ type: String, index: true })
  liveSessionId: string;

  @Prop({ type: String, index: true })
  liveSessionGroupId: string;

  @Prop({ type: String, required: false })
  commentId: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const OrderSchema = SchemaFactory.createForClass(Order);

OrderSchema.virtual('liveSession', {
  ref: 'LiveSession',
  localField: 'liveSessionId',
  foreignField: 'id',
  justOne: true,
});

OrderSchema.virtual('comment', {
  ref: 'Comment',
  localField: 'commentId',
  foreignField: 'id',
  justOne: true,
});

OrderSchema.virtual('platformUser', {
  ref: 'PlatformUser',
  localField: 'platformUserId',
  foreignField: 'id',
  justOne: true,
});

OrderSchema.virtual('platformChannel', {
  ref: 'PlatformChannel',
  localField: 'platformChannelId',
  foreignField: 'id',
  justOne: true,
});

OrderSchema.virtual('liveSessionGroup', {
  ref: 'LiveSessionGroup',
  localField: 'liveSessionGroupId',
  foreignField: 'id',
  justOne: true,
});

OrderSchema.set('toJSON', { virtuals: true });
OrderSchema.set('toObject', { virtuals: true });
