import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthController } from './auth.controller';
import {
  SystemUser,
  SystemUserSchema,
  Subscription,
  SubscriptionSchema,
} from '@common/schemas';
import { AuthService } from './services/auth.service';
import { JwtModule } from '@nestjs/jwt';
import { RefreshTokenService } from '@module/auth/services/refresh-token.service';
import {
  RefreshToken,
  RefreshTokenSchema,
} from '@common/schemas/refresh-token.schema';
import { SchemaCollectionName } from '@common/constants/schema';
import { SubscriptionModule } from '@module/subscription/subscription.module';
import { LiveSessionModule } from '@module/live-session/live-session.module';
import { LiveSessionGroupModule } from "@module/live-session-group/live-session-group.module";

@Module({
  imports: [
    LoggerModule,
    JwtModule.register({}),
    LiveSessionModule,
    LiveSessionGroupModule,
    SubscriptionModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: RefreshToken.name,
        schema: RefreshTokenSchema,
        collection: SchemaCollectionName.RefreshToken,
      },
      {
        name: Subscription.name,
        schema: SubscriptionSchema,
        collection: SchemaCollectionName.Subscription,
      },
    ]),
  ],
  controllers: [AuthController],
  providers: [AuthService, RefreshTokenService],
  exports: [AuthService, RefreshTokenService],
})
export class AuthModule {}
