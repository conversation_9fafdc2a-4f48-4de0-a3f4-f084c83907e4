import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Order, OrderDocument } from '@common/schemas/order.schema';
import { CommentService } from '@module/comment/comment.service';
import { OrderDto } from '@module/order/dtos/order.dto';
import { GetOrderDto } from '@module/order/dtos/get-order.dto';
import { OrderPaginationResultDto } from '@module/order/dtos/order-pagination.dto';
import OrderMapper from '@module/order/order.mapper';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { PaginationService } from '@shared/services/pagination.service';
import { AppException } from '@common/exceptions/app-exception';
import {
  ORDER_NOT_EXIST,
  ORDER_ACCESS_FORBIDDEN,
} from '@common/exceptions/error';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelDocument,
} from '@common/schemas/system-user-platform-channel.schema';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class OrderService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Order.name)
    private orderModel: Model<OrderDocument>,
    @InjectModel(SystemUserPlatformChannel.name)
    private systemUserPlatformChannelModel: Model<SystemUserPlatformChannelDocument>,
    private readonly commentService: CommentService,
    private readonly paginationService: PaginationService,
  ) {
    this.logger.setContext(OrderService.name);
  }

  /**
   * Get platform channel IDs that the user has access to
   */
  private async getUserAuthorizedPlatformChannelIds(
    userId: string,
  ): Promise<string[]> {
    this.logger.log(`Getting authorized platform channels for user: ${userId}`);

    const userPlatformChannels = await this.systemUserPlatformChannelModel
      .find({ systemUser: userId })
      .select('platformChannel')
      .exec();

    return userPlatformChannels.map(upc => upc.platformChannel);
  }

  /**
   * Check if user has access to a specific platform channel
   */
  private async checkUserPlatformChannelAccess(
    userId: string,
    platformChannelId: string,
  ): Promise<boolean> {
    const accessRecord = await this.systemUserPlatformChannelModel
      .findOne({
        systemUser: userId,
        platformChannel: platformChannelId,
      })
      .exec();

    return !!accessRecord;
  }

  async createFromComment(
    commentId: string,
    createdById: string,
  ): Promise<OrderDto> {
    this.logger.log(
      `Creating order from comment ${commentId} by user ${createdById}`,
    );

    // Fetch the comment with populated relations
    const comment = await this.commentService.findById(commentId);

    // Generate a unique order code
    const orderCode = `ORD-${Date.now()}-${uuidv4()
      .substring(0, 8)
      .toUpperCase()}`;

    // Create the order based on comment data
    const newOrder = new this.orderModel({
      orderCode,
      createdById,
      platformChannelId: comment.liveSession?.platformChannelId || null,
      platformUserId: comment.platformUserId,
      liveSessionId: comment.liveSessionId,
      commentId: comment.id,
    });

    try {
      const savedOrder = await newOrder.save();
      this.logger.log(`Order created with ID: ${savedOrder.id}`);

      // Fetch the order with populated relations for response
      const orderWithRelations = await this.orderModel
        .findOne({ id: savedOrder.id })
        .populate({
          path: 'comment',
          populate: {
            path: 'platformUser',
          },
        })
        .populate({
          path: 'liveSession',
          populate: {
            path: 'platformChannel',
            populate: {
              path: 'creator',
            },
          },
        })
        .populate({
          path: 'platformChannel',
          populate: {
            path: 'creator',
          },
        })
        .exec();

      return OrderMapper.toDto(orderWithRelations);
    } catch (error) {
      this.logger.error('Error creating order from comment', error);
      throw error;
    }
  }

  async findAll(
    options: PaginationOptionsDto,
    filters: GetOrderDto,
    currentUserId: string,
  ): Promise<OrderPaginationResultDto> {
    this.logger.log(
      `Finding orders with pagination and filters for user: ${currentUserId}`,
    );

    // Get platform channels the user has access to
    const authorizedPlatformChannelIds =
      await this.getUserAuthorizedPlatformChannelIds(currentUserId);

    if (authorizedPlatformChannelIds.length === 0) {
      this.logger.log(
        `User ${currentUserId} has no authorized platform channels`,
      );
      return {
        data: [],
        meta: {
          total: 0,
          page: options.page || 1,
          limit: options.limit || 10,
          pages: 0,
        },
      };
    }

    const filterQuery: FilterQuery<OrderDocument> = {
      // Authorization filter: only orders from user's platform channels
      platformChannelId: { $in: authorizedPlatformChannelIds },
    };

    // Apply filters
    if (filters.platformUserId) {
      filterQuery.platformUserId = filters.platformUserId;
    }

    if (filters.platformChannelId) {
      filterQuery.platformChannelId = filters.platformChannelId;
    }

    if (filters.commentId) {
      filterQuery.commentId = filters.commentId;
    }

    if (filters.liveSessionId) {
      filterQuery.liveSessionId = filters.liveSessionId;
    }

    if (filters.search) {
      filterQuery.$or = [
        { orderCode: { $regex: filters.search, $options: 'i' } },
      ];
    }

    // Use custom pagination to support population
    const { page, limit, sort } =
      PaginationService.formatPaginationOptions(options);

    // Count total documents
    const total = await this.orderModel.countDocuments(filterQuery).exec();

    // Create query with population
    const orders = await this.orderModel
      .find(filterQuery)
      .sort(sort)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate({
        path: 'comment',
        populate: {
          path: 'platformUser',
        },
      })
      .populate({
        path: 'liveSession',
        populate: {
          path: 'platformChannel',
          populate: {
            path: 'creator',
          },
        },
      })
      .populate({
        path: 'platformChannel',
        populate: {
          path: 'creator',
        },
      })
      .exec();

    const pages = Math.ceil(total / limit);
    const meta = { total, page, limit, pages };

    return {
      data: orders.map(order => OrderMapper.toDto(order)),
      meta,
    };
  }

  async findById(orderId: string, currentUserId: string): Promise<OrderDto> {
    this.logger.log(
      `Finding order by ID: ${orderId} for user: ${currentUserId}`,
    );

    const order = await this.orderModel
      .findOne({ id: orderId })
      .populate({
        path: 'comment',
        populate: {
          path: 'platformUser',
        },
      })
      .populate({
        path: 'liveSession',
        populate: {
          path: 'platformChannel',
          populate: {
            path: 'creator',
          },
        },
      })
      .populate({
        path: 'platformChannel',
        populate: {
          path: 'creator',
        },
      })
      .exec();

    if (!order) {
      throw new AppException(ORDER_NOT_EXIST);
    }

    // Check if user has access to this order's platform channel
    const hasAccess = await this.checkUserPlatformChannelAccess(
      currentUserId,
      order.platformChannelId,
    );

    if (!hasAccess) {
      this.logger.warn(
        `User ${currentUserId} attempted to access order ${orderId} without permission`,
      );
      throw new AppException(ORDER_ACCESS_FORBIDDEN);
    }

    return OrderMapper.toDto(order);
  }
}
