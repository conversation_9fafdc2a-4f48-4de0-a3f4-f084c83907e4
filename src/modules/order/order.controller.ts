import { <PERSON>, Post, Param, Get, Query } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiOkResponse,
} from '@nestjs/swagger';
import { OrderService } from '@module/order/order.service';
import { OrderDto } from '@module/order/dtos/order.dto';
import { GetOrderDto } from '@module/order/dtos/get-order.dto';
import { OrderPaginationResultDto } from '@module/order/dtos/order-pagination.dto';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { Auth, CurrentUser } from '@common/decorators';
import { SystemUserDocument } from '@common/schemas';

@ApiTags('Orders')
@Controller('orders')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Auth()
  @Get()
  @ApiOperation({
    summary: 'Get orders with pagination and filtering',
    description:
      'Retrieves a paginated list of orders with optional filtering. Requires authentication.',
  })
  @ApiOkResponse({
    type: OrderPaginationResultDto,
    description: 'Orders retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - authentication required',
  })
  async getOrders(
    @Query() paginationOptions: PaginationOptionsDto,
    @Query() filters: GetOrderDto,
    @CurrentUser() currentUser: SystemUserDocument,
  ): Promise<OrderPaginationResultDto> {
    return this.orderService.findAll(
      paginationOptions,
      filters,
      currentUser.id,
    );
  }

  @Auth()
  @Get(':id')
  @ApiOperation({
    summary: 'Get order by ID',
    description:
      'Retrieves a single order by its ID with all populated relations. Requires authentication.',
  })
  @ApiParam({
    name: 'id',
    description: 'The order ID to retrieve',
    type: 'string',
  })
  @ApiOkResponse({
    type: OrderDto,
    description: 'Order retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - authentication required',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - no access to this order',
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  async getOrderById(
    @Param('id') orderId: string,
    @CurrentUser() currentUser: SystemUserDocument,
  ): Promise<OrderDto> {
    return this.orderService.findById(orderId, currentUser.id);
  }

  @Auth()
  @Post('from-comments/:id')
  @ApiOperation({
    summary: 'Create order from comment',
    description:
      'Creates a new order based on comment data. Requires authentication.',
  })
  @ApiParam({
    name: 'id',
    description: 'The comment ID to create order from',
    type: 'string',
  })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
    type: OrderDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Comment not found',
  })
  async createOrderFromComment(
    @Param('id') commentId: string,
    @CurrentUser() currentUser: SystemUserDocument,
  ): Promise<OrderDto> {
    return this.orderService.createFromComment(commentId, currentUser.id);
  }
}
