import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { Order, OrderSchema } from '@common/schemas/order.schema';
import {
  PlatformChannel,
  PlatformChannelSchema,
} from '@common/schemas/platform-channel.schema';
import {
  LiveSession,
  LiveSessionSchema,
} from '@common/schemas/live-session.schema';
import {
  SystemUser,
  SystemUserSchema,
} from '@common/schemas/system-user.schema';
import {
  PlatformUser,
  PlatformUserSchema,
} from '@common/schemas/platform-user.schema';
import { Comment, CommentSchema } from '@common/schemas/comment.schema';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelSchema,
} from '@common/schemas/system-user-platform-channel.schema';
import { OrderService } from '@module/order/order.service';
import { OrderController } from '@module/order/order.controller';
import { CommentModule } from '@module/comment/comment.module';
import { PaginationService } from '@shared/services/pagination.service';

@Module({
  imports: [
    LoggerModule,
    CommentModule,
    MongooseModule.forFeature([
      {
        name: Order.name,
        schema: OrderSchema,
        collection: SchemaCollectionName.Order,
      },
      {
        name: PlatformChannel.name,
        schema: PlatformChannelSchema,
        collection: SchemaCollectionName.PlatformChannel,
      },
      {
        name: LiveSession.name,
        schema: LiveSessionSchema,
        collection: SchemaCollectionName.LiveSession,
      },
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: PlatformUser.name,
        schema: PlatformUserSchema,
        collection: SchemaCollectionName.PlatformUser,
      },
      {
        name: Comment.name,
        schema: CommentSchema,
        collection: SchemaCollectionName.Comment,
      },
      {
        name: SystemUserPlatformChannel.name,
        schema: SystemUserPlatformChannelSchema,
        collection: SchemaCollectionName.SystemUserPlatformChannel,
      },
    ]),
  ],
  controllers: [OrderController],
  providers: [OrderService, PaginationService],
  exports: [OrderService],
})
export class OrderModule {}
