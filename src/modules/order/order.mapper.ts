import { OrderDocument } from '@common/schemas/order.schema';
import { OrderDto } from '@module/order/dtos/order.dto';
import { CommentDto } from '@module/comment/dtos/comment.dto';
import { PlatformUserDto } from '@module/platform-user/dtos/platform-user.dto';
import { LiveSessionDto } from '@module/live-session/dtos/live-session.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { CreatorDto } from '@module/user/dtos/creator.dto';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';

class OrderMapper {
  static toDto(order: OrderDocument): OrderDto {
    return new OrderDto({
      id: order.id,
      orderCode: order.orderCode,
      createdById: order.createdById,
      platformChannelId: order.platformChannelId,
      platformUserId: order.platformUserId,
      liveSessionId: order.liveSessionId,
      liveSessionGroupId: order.liveSessionGroupId,
      commentId: order.commentId,
      comment: order.comment
        ? new CommentDto({
            id: order.comment.id,
            commentId: order.comment.commentId,
            message: order.comment.message,
            platformUserId: order.comment.platformUserId,
            platformUser: order.comment.platformUser
              ? new PlatformUserDto({
                  id: order.comment.platformUser.id,
                  userId: order.comment.platformUser.userId,
                  nickname: order.comment.platformUser.nickname,
                  username: order.comment.platformUser.uniqueId,
                  hasCanceledOrder:
                    order.comment.platformUser.hasCanceledOrder || false,
                  livestreamPlatform:
                    order.comment.platformUser.livestreamPlatform,
                  profilePictureUrl:
                    order.comment.platformUser.profilePictureUrl,
                  phoneNumber: order.comment.platformUser.phoneNumber,
                  createdAt: order.comment.platformUser.createdAt,
                })
              : undefined,
            createdAt: order.comment.createdAt,
          })
        : undefined,
      liveSession: order.liveSession
        ? new LiveSessionDto({
            id: order.liveSession.id,
            roomId: order.liveSession.roomId,
            roomTitle: order.liveSession.roomTitle,
            shareLink: order.liveSession.shareLink,
            livestreamPlatform: order.liveSession.livestreamPlatform,
            platformChannelId: order.liveSession.platformChannelId,
            createdAt: order.liveSession.createdAt,
          })
        : undefined,
      liveSessionGroup: order.liveSessionGroup
        ? new LiveSessionGroupDto({
            id: order.liveSessionGroup.id,
            name: order.liveSessionGroup.name,
            createdAt: order.liveSessionGroup.createdAt,
          })
        : undefined,
      platformChannel: order.platformChannel
        ? new PlatformChannelDto({
            id: order.platformChannel.id,
            channelId: order.platformChannel.channelId,
            name: order.platformChannel.name,
            recognizeId: order.platformChannel.recognizeId,
            livestreamPlatform: order.platformChannel.livestreamPlatform,
            createdBy: undefined,
          })
        : undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    });
  }
}

export default OrderMapper;
