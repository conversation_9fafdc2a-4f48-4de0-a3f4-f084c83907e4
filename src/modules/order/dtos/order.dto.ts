import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { CommentDto } from '@module/comment/dtos/comment.dto';
import { LiveSessionDto } from '@module/live-session/dtos/live-session.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';

export class OrderDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  orderCode: string;

  @ApiProperty()
  @Expose()
  createdById: string;

  @ApiProperty()
  @Expose()
  platformChannelId: string;

  @ApiProperty()
  @Expose()
  platformUserId: string;

  @ApiProperty()
  @Expose()
  liveSessionId: string;

  @ApiProperty({ nullable: true })
  @Expose()
  liveSessionGroupId: string | null;

  @ApiProperty()
  @Expose()
  commentId: string;

  @ApiProperty({ type: () => CommentDto })
  @Expose()
  comment?: CommentDto;

  @ApiProperty({ type: () => LiveSessionDto })
  @Expose()
  liveSession?: LiveSessionDto;

  @ApiProperty({ type: () => LiveSessionGroupDto })
  @Expose()
  liveSessionGroup?: LiveSessionGroupDto;

  @ApiProperty({ type: () => PlatformChannelDto })
  @Expose()
  platformChannel?: PlatformChannelDto;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;

  constructor(partial: Partial<OrderDto>) {
    Object.assign(this, partial);
  }
}
