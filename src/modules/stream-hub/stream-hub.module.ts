import { LoggerModule } from '@common/logger/logger.module';
import { AppLogger } from '@common/logger/logger.service';
import { Module } from '@nestjs/common';
import { LiveManagerModule } from '@module/live-manager/live-manager.module';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import {
  PlatformUser,
  PlatformUserSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { MongooseModule } from '@nestjs/mongoose';
import { WsPermissionsGuard } from '@common/guards/ws-permission.guard';
import { JwtService } from '@nestjs/jwt';
import { APP_FILTER } from '@nestjs/core';
import { WebSocketExceptionFilter } from '@common/filters/websocket.filter';
import { SchemaCollectionName } from '@common/constants/schema';
import { PlatformChannelModule } from '@module/platform-channel/platform-channel.module';
import { SystemConfigModule } from '@module/system-config/system-config.module';
import { LiveSessionModule } from '@module/live-session/live-session.module';
import { ScheduleModule } from '@nestjs/schedule';
import { RedisModule } from '@shared/redis';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StreamHubGateway } from '@module/stream-hub/stream-hub.gateway';
import { StreamPlatformFactory } from '@module/stream-hub/platforms/stream-platform.factory';
import { StreamHubCommentService } from '@module/stream-hub/services/stream-hub-comment.service';
import { LiveCommentModule } from '@module/live-comment/live-comment.module';
import { StreamHubProviderWatcher } from '@module/stream-hub/watchers/stream-hub-provider.watcher';
import { StreamHubRedisWatcher } from '@module/stream-hub/watchers/providers/redis/stream-hub-redis.watcher';
import { CommentModule } from '@module/comment/comment.module';
import {
  PlatformChannel,
  PlatformChannelSchema,
} from '@common/schemas/platform-channel.schema';
import { LiveSessionGroupModule } from "@module/live-session-group/live-session-group.module";

@Module({
  imports: [
    LoggerModule,
    LiveManagerModule,
    PlatformUserModule,
    PlatformChannelModule,
    LiveCommentModule,
    SystemConfigModule,
    CommentModule,
    LiveSessionGroupModule,
    ScheduleModule.forRoot(),
    LiveSessionModule,
    RedisModule.forRootAsync({
      imports: [ConfigModule, LoggerModule],
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: PlatformChannel.name,
        schema: PlatformChannelSchema,
        collection: SchemaCollectionName.PlatformChannel,
      },
      {
        name: PlatformUser.name,
        schema: PlatformUserSchema,
        collection: SchemaCollectionName.PlatformUser,
      },
    ]),
  ],
  providers: [
    StreamHubGateway,
    StreamPlatformFactory,
    StreamHubRedisWatcher,
    StreamHubProviderWatcher,
    StreamHubCommentService,
    AppLogger,
    WsPermissionsGuard,
    JwtService,
    {
      provide: APP_FILTER,
      useClass: WebSocketExceptionFilter,
    },
  ],
  exports: [],
})
export class StreamHubModule {}
