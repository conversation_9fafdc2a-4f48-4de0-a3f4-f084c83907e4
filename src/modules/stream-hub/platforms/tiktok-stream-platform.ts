import { BaseStreamPlatform } from '@module/stream-hub/platforms/base-stream-platform';
import { LivestreamPlatform } from '@common/constants';
import { Injectable } from '@nestjs/common';
import { LiveSessionDocument } from '@common/schemas';
import { TikTokCommentMapper } from '@module/live-comment/mappers/tiktok-comment.mapper';
import { WebcastPushConnection } from '@vulinhscoris/tiktok-live-connector';
import { ensureTrailingSlash } from '@common/utils/link';
import { HEADLESS_SERVER } from '@common/config/enviroment.config';
import {
  TikTokClientComment,
  TikTokPlatformComment,
} from '@module/live-comment/types/tiktok';
import * as dayjs from 'dayjs';

@Injectable()
export class TiktokStreamPlatform extends BaseStreamPlatform {
  private commentMapper: TikTokCommentMapper = new TikTokCommentMapper();

  get name() {
    return LivestreamPlatform.TIKTOK;
  }

  async handle() {
    const client = await this.getClient();

    this.streamHubProviderWatcher
      .getProvider()
      .bindDisconnectToSessionId(this.getSessionId(), () =>
        client.disconnect(),
      );

    let liveSession: LiveSessionDocument;

    const liveState = client.getState() as {
      roomId: string;
      roomInfo?: {
        title?: string;
        share_url?: string;
      };
    };

    const roomId = liveState.roomId;

    this.streamHubCommentService.setRoomId(roomId);

    await this.streamHubCommentService.sendPreviousComments();

    if (this.getConfig().saveHistory) {
      const today = dayjs().format('DD-MM-YYYY');

      liveSession = await this.liveSessionService.start(this.getSessionId(), {
        userId: this.getConfig().userId,
        roomId,
        deviceId: this.getSocket().id,
        roomTitle: `Livestream TikTok ngày ${today}`,
        shareLink: `https://m.tiktok.com/share/live/${roomId}/?language=en`,
        livestreamPlatform: this.name,
        platformChannelId: this.getConfig().platformChannelId,
      });
    }

    // @ts-ignore
    client.on('chat', async (data: TikTokPlatformComment) => {
      const hasSent = await this.streamHubProviderWatcher
        .getProvider()
        .hasCommentBeenSent(this.getSessionId(), data.msgId);

      if (!hasSent) {
        const comment = {
          msgId: data.msgId,
          profilePictureUrl: data.profilePictureUrl,
          nickName: data.nickname,
          username: data.uniqueId,
          message: data.comment,
          userId: data.userId,
        } as TikTokClientComment;

        await this.streamHubCommentService.mapExtraInformationToComment(
          comment,
        );

        await this.streamHubProviderWatcher
          .getProvider()
          .addCommentHasBeenSent(this.getSessionId(), data.msgId);

        if (liveSession) {
          const platformUser = await this.platformUserService.upsertUser(
            this.commentMapper.fromPlatformToPlatformUser(data),
          );
          const addedComment = await this.commentService.addCommentToLive({
            commentId: data.msgId,
            liveSessionId: liveSession.id,
            roomId: liveSession.roomId,
            message: data.comment,
            platformUserId: platformUser.id,
          });

          comment.id = addedComment.id;
        }

        this.streamHubCommentService.sendToClient(comment);
      }
    });

    // @ts-ignore
    client.on('streamEnd', async (actionId: string) => {
      this.logger.log(`Stream ended with actionId: ${actionId}`);
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });

    // @ts-ignore
    client.on('error', async error => {
      this.logger.error(`Unexpected error: ${error.message}`, error.stack);
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });
  }

  async getClient(): Promise<WebcastPushConnection> {
    const shouldPrioritizeBackupTiktok =
      await this.systemConfigService.shouldPrioritizeBackupTiktok();

    this.logger.log(`Tiktok backup priority: ${shouldPrioritizeBackupTiktok}`);

    const connections = [
      () => this.createDefaultConnection(this.platformChannel.channelId),
      () => this.createBackupConnection(this.platformChannel.channelId),
    ];

    if (shouldPrioritizeBackupTiktok) {
      connections.reverse();
    }

    for (const connection of connections) {
      try {
        return await connection();
      } catch (error) {
        this.logger.error(
          `Failed to connect to TikTok with ${connection.name}: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  private async createDefaultConnection(
    username: string,
  ): Promise<WebcastPushConnection> {
    const tiktokLiveConnection = new WebcastPushConnection(username);

    const state = await tiktokLiveConnection.connect();

    this.logger.log(
      `Connected to roomId ${state.roomId} with userId ${username}`,
    );

    return tiktokLiveConnection;
  }

  private async createBackupConnection(
    username: string,
  ): Promise<WebcastPushConnection> {
    const tiktokLiveConnection = new WebcastPushConnection(username, {
      signProviderOptions: {
        timeout: 20000,
        host: ensureTrailingSlash(HEADLESS_SERVER),
        params: {
          username,
          apiKey: process.env.X_API_KEY,
        },
        only: true,
      },
    });

    const state = await tiktokLiveConnection.connect();

    this.logger.log(
      `[Backup] Connected to roomId ${state.roomId} with userId ${username}`,
    );

    return tiktokLiveConnection;
  }
}
