import { LivestreamPlatform } from '@common/constants';
import { StreamHubInput } from '@module/stream-hub/types';
import { AppLogger } from '@common/logger/logger.service';
import { Socket } from 'socket.io';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import {
  FORBIDDEN,
  LIVE_NOT_EXIST,
  PLATFORM_CHANNEL_NOT_EXIST,
} from '@common/exceptions/error';
import { v4 } from 'uuid';
import { StreamHubCommentService } from '@module/stream-hub/services/stream-hub-comment.service';
import { StreamHubProviderWatcher } from '@module/stream-hub/watchers/stream-hub-provider.watcher';
import { ConfigService } from '@nestjs/config';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { CommentService } from '@module/comment/comment.service';
import { SystemConfigService } from '@module/system-config/system-config.service';
import { PlatformChannelDocument } from '@common/schemas/platform-channel.schema';
import { Model } from 'mongoose';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { LiveSessionGroupDocument } from '@common/schemas';

export abstract class BaseStreamPlatform {
  protected socket: Socket;
  protected config: StreamHubInput;
  protected roomId: string;
  protected sessionId: string;
  protected liveSessionGroup: LiveSessionGroupDocument;
  protected platformChannel: PlatformChannelDocument;

  constructor(
    protected readonly logger: AppLogger,
    protected readonly systemConfigService: SystemConfigService,
    protected readonly platformUserService: PlatformUserService,
    protected readonly platformChannelService: PlatformChannelService,
    protected readonly streamHubCommentService: StreamHubCommentService,
    protected readonly commentService: CommentService,
    protected readonly streamHubProviderWatcher: StreamHubProviderWatcher,
    protected readonly liveSessionService: LiveSessionService,
    protected readonly configService: ConfigService,
    protected readonly platformChannelModel: Model<PlatformChannelDocument>,
  ) {
    this.logger.setContext(this.constructor.name);
  }

  abstract get name(): LivestreamPlatform;

  abstract handle(): Promise<void>;

  setSocket(socket: Socket): void {
    this.socket = socket;
  }

  setConfig(config: StreamHubInput): void {
    this.config = config;
  }

  setLiveSessionGroup(liveSessionGroup: LiveSessionGroupDocument): void {
    this.liveSessionGroup = liveSessionGroup;
  }

  fetchRoomId(): Promise<string> {
    if (!this.config.platformChannelId) {
      this.logger.error('Platform channel ID is not set in the configuration.');
      throw new Error('Platform channel ID is not set.');
    }

    if (this.roomId) return Promise.resolve(this.roomId);

    return this.platformChannelService.getRoomIdById(
      this.config.platformChannelId,
    );
  }

  getSessionId(): string {
    return this.sessionId;
  }

  getConfig(): StreamHubInput {
    return this.config;
  }

  getSocket(): Socket {
    return this.socket;
  }

  getLiveSessionGroup(): LiveSessionGroupDocument {
    return this.liveSessionGroup;
  }

  generateSessionId(): string {
    return v4();
  }

  async start(socket: Socket, config: StreamHubInput): Promise<void> {
    this.socket = socket;
    this.config = config;
    this.sessionId = this.generateSessionId();

    this.logger.log(
      `Initializing ${config.platformChannelId} for user ${config.userId} ...`,
    );

    const roomId = await this.fetchRoomId();

    this.roomId = roomId;

    this.streamHubCommentService.init({
      socket,
      config,
      platform: this.name,
      roomId,
      sessionId: this.getSessionId(),
    });

    const provider = this.streamHubProviderWatcher.getProvider();

    await provider.store({
      sessionId: this.getSessionId(),
      userId: config.userId,
      platformChannelId: config.platformChannelId,
      roomId,
      socketId: socket.id,
    });

    // Initialize the platform-specific logic
    await this.handle();

    this.logger.log(
      `Stream ${config.platformChannelId} started successfully for user ${config.userId}.`,
    );
  }

  async validate(): Promise<{
    isValid: boolean;
    errorCode?: string;
    errorMessage?: string;
  }> {
    const platformChannel = await this.platformChannelModel.findOne({
      id: this.config.platformChannelId,
    });

    if (!platformChannel) {
      this.logger.error(
        `Platform channel with ID ${this.config.platformChannelId} does not exist.`,
      );
      return {
        isValid: false,
        errorCode: PLATFORM_CHANNEL_NOT_EXIST.errorCode,
        errorMessage: PLATFORM_CHANNEL_NOT_EXIST.errorMessage,
      };
    }

    this.platformChannel = platformChannel;

    const canAccess = await this.platformChannelService.canAccess(
      this.config.userId,
      this.config.platformChannelId,
    );

    if (!canAccess) {
      this.logger.error(
        `User with ID ${this.config.userId} does not have access to platform channel ${this.config.platformChannelId}.`,
      );
      return {
        isValid: false,
        errorCode: FORBIDDEN.errorCode,
        errorMessage: FORBIDDEN.errorMessage,
      };
    }

    const roomId = await this.fetchRoomId();

    if (!roomId) {
      this.logger.error(
        `Live ID not found for platform channel with ID ${this.config.platformChannelId}.`,
      );
      return {
        isValid: false,
        errorCode: LIVE_NOT_EXIST.errorCode,
        errorMessage: LIVE_NOT_EXIST.errorMessage,
      };
    }

    this.logger.log(
      `User with ID ${this.config.userId} has access to room ID ${roomId} on platform channel ${this.config.platformChannelId}.`,
    );

    return {
      isValid: true,
    };
  }
}
