import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  WsException,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { RedisConnectionManagerService } from '@shared/redis/redis-connection-manager.service';
import { WsPermissionsGuard } from '@common/guards/ws-permission.guard';
import { Auth } from '@common/decorators';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import { UserPayload } from '@module/user/types';
import * as AppError from '@common/exceptions/error';
import { StreamPlatformFactory } from '@module/stream-hub/platforms/stream-platform.factory';
import { StreamHubEvent } from '@module/stream-hub/constants';
import { StreamHubProviderWatcher } from '@module/stream-hub/watchers/stream-hub-provider.watcher';
import { UN_AUTHORIZED } from '@common/exceptions/error';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { LiveSessionGroupService } from '@module/live-session-group/live-session-group.service';

@WebSocketGateway({
  cors: {
    credentials: true,
  },
})
export class StreamHubGateway implements OnGatewayInit, OnGatewayConnection {
  @WebSocketServer() server: Server;
  private logger: Logger = new Logger('LiveCommentGateway');

  static INTERVAL: Record<string, NodeJS.Timer> = {};

  constructor(
    private readonly platformChannelService: PlatformChannelService,
    private readonly liveSessionGroupService: LiveSessionGroupService,
    private readonly streamHubFactory: StreamPlatformFactory,
    private readonly streamHubProviderWatcher: StreamHubProviderWatcher,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  afterInit(server: Server) {
    const provider = this.streamHubProviderWatcher.getProvider();

    provider.applyAdapter(server);

    provider.listenStopSocket();
    provider.listenStopPlatformChannelId();
    provider.listenStopSession();
    provider
      .listenCleanup()
      .then(() =>
        this.logger.log('StreamHubProviderWatcher initialized successfully.'),
      )
      .catch(error =>
        this.logger.error(
          'Error initializing StreamHubProviderWatcher:',
          error.message,
          error.stack,
        ),
      );
  }

  async handleConnection(client: Socket) {
    const authHeader = client.handshake.headers['authorization'];

    const accessToken = authHeader.split(' ')[1];
    let auth;

    try {
      auth = this.jwtService.verify(accessToken, {
        secret: this.configService.get('JWT_ACCESS_TOKEN_SECRET'),
      });

      if (!auth || !auth.userId) {
        client.emit(StreamHubEvent.LIVE_ERROR, UN_AUTHORIZED);
        client.disconnect();
        return;
      }

      this.logger.log(
        `Client connected: ${client.id} with userId: ${auth?.userId}`,
      );

      const intervalId = setInterval(async () => {
        const streamHubOutputs = await this.streamHubProviderWatcher
          .getProvider()
          .getByUserId(auth.userId);

        const hasLiveSession = streamHubOutputs.some(
          output => !!output.sessionId,
        );

        if (!hasLiveSession) return;

        await this.streamHubProviderWatcher
          .getProvider()
          .addDailyUsage(1, auth.userId);
      }, 60 * 1000);

      StreamHubGateway.INTERVAL[client.id] = intervalId;
    } catch (error) {
      client.emit(StreamHubEvent.LIVE_ERROR, UN_AUTHORIZED);
      client.disconnect();

      this.logger.error(
        `Unauthorized access attempt by client ${client.id}: ${error.message}`,
        JSON.stringify(error),
      );

      return;
    }

    // Handle client disconnect
    client.on('disconnect', async () => {
      try {
        this.streamHubProviderWatcher
          .getProvider()
          .triggerSocketStop(client.id);
        if (StreamHubGateway.INTERVAL[client.id]) {
          clearInterval(StreamHubGateway.INTERVAL[client.id]);
          delete StreamHubGateway.INTERVAL[client.id];
        }
        this.logger.log(
          `Client disconnected: ${client.id} with userId: ${auth?.userId}`,
        );
      } catch (error) {
        this.logger.error(
          `Error handling disconnect for client ${client.id}: ${error.message}`,
          JSON.stringify(error),
        );
      }
    });

    // Handle client errors
    client.on('error', async err => {
      try {
        this.streamHubProviderWatcher
          .getProvider()
          .triggerSocketStop(client.id);
        if (StreamHubGateway.INTERVAL[client.id]) {
          clearInterval(StreamHubGateway.INTERVAL[client.id]);
          delete StreamHubGateway.INTERVAL[client.id];
        }
        this.logger.error(
          `Client error: ${client.id} with auth: ${auth?.userId}`,
          JSON.stringify(err),
        );
      } catch (error) {
        this.logger.error(
          `Error handling client error for ${client.id}: ${error.message}`,
          error.stack,
        );
      }
    });
  }

  @Auth()
  @UseGuards(WsPermissionsGuard)
  @SubscribeMessage('stream-hub:stop')
  async stopLiveSession(
    @ConnectedSocket()
    socket: Socket,
    @MessageBody('platformChannelId') platformChannelId: string,
  ): Promise<void> {
    this.logger.log(
      `Received message stop live session for platformChannelId: ${platformChannelId}`,
    );

    if (!platformChannelId) {
      socket.emit(StreamHubEvent.LIVE_ERROR, {
        errorCode: AppError.INVALID_PLATFORM_CHANNEL_ID.errorCode,
        message: AppError.INVALID_PLATFORM_CHANNEL_ID.errorMessage,
      });

      return;
    }

    this.streamHubProviderWatcher
      .getProvider()
      .triggerPlatformChannelStop(platformChannelId);
  }

  @Auth()
  @UseGuards(WsPermissionsGuard)
  @SubscribeMessage('stream-hub:start')
  async handleStartLiveSession(
    @ConnectedSocket()
    socket: Socket,
    @MessageBody('platformChannelId') platformChannelId: string,
    @MessageBody('user') user: UserPayload,
    @MessageBody('saveHistory') saveHistory = true,
  ): Promise<void> {
    this.logger.log(
      `Received message start live session for platformChannelId: ${platformChannelId} and user: ${user.username}`,
    );

    if (!platformChannelId) {
      socket.emit(StreamHubEvent.LIVE_ERROR, {
        errorCode: AppError.INVALID_PLATFORM_CHANNEL_ID.errorCode,
        message: AppError.INVALID_PLATFORM_CHANNEL_ID.errorMessage,
      });
      return;
    }

    const platformChannel = await this.platformChannelService.get(
      platformChannelId,
    );

    if (!platformChannel) {
      socket.emit(StreamHubEvent.LIVE_ERROR, {
        errorCode: AppError.PLATFORM_CHANNEL_NOT_EXIST.errorCode,
        message: AppError.PLATFORM_CHANNEL_NOT_EXIST.errorMessage,
      });
      return;
    }

    const streamPlatform = this.streamHubFactory.create(
      platformChannel.livestreamPlatform,
    );

    streamPlatform.setConfig({
      platformChannelId,
      userId: user.id,
      saveHistory,
    });
    streamPlatform.setSocket(socket);

    const validation = await streamPlatform.validate();

    if (!validation.isValid) {
      this.logger.error(
        `Validation failed for platform channel ${platformChannelId}: ${validation.errorCode}`,
      );
      socket.emit(StreamHubEvent.LIVE_ERROR, {
        errorCode: validation.errorCode,
        message: validation.errorMessage,
      });
      return;
    }

    try {
      await streamPlatform.start(socket, {
        platformChannelId,
        userId: user.id,
        saveHistory,
      });
    } catch (error) {
      this.logger.error(
        `Failed to start live session for platform channel ${platformChannel.id}: ${error.message}`,
        error,
      );
      socket.emit(StreamHubEvent.LIVE_ERROR, {
        errorCode: AppError.START_LIVE_SESSION_FAILED.errorCode,
        message: AppError.START_LIVE_SESSION_FAILED.errorMessage,
      });
    }
  }
}
