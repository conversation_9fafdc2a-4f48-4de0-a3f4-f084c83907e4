import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Comment, CommentDocument } from '@common/schemas/comment.schema';
import { AppException } from '@common/exceptions/app-exception';
import { COMMENT_NOT_EXIST } from '@common/exceptions/error';

@Injectable()
export class CommentService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Comment.name)
    private commentModel: Model<CommentDocument>,
  ) {
    this.logger.setContext(CommentService.name);
  }

  async findById(commentId: string): Promise<CommentDocument> {
    const comment = await this.commentModel
      .findOne({ id: commentId })
      .populate('platformUser')
      .populate('liveSession')
      .exec();

    if (!comment) {
      throw new AppException(COMMENT_NOT_EXIST);
    }

    return comment;
  }

  async addCommentToLive(data: {
    commentId: string;
    message: string;
    liveSessionId: string;
    liveSessionGroupId: string;
    roomId: string;
    platformUserId?: string;
  }): Promise<Comment> {
    const newComment = new this.commentModel({
      commentId: data.commentId,
      message: data.message,
      liveSessionId: data.liveSessionId,
      roomId: data.roomId,
      platformUserId: data.platformUserId,
      liveSessionGroupId: data.liveSessionGroupId,
    });

    try {
      return await newComment.save();
    } catch (error) {
      this.logger.error('Error saving comment to live session', error);
      throw error;
    }
  }
}
