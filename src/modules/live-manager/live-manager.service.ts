import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { LiveSession } from '@common/schemas';
import { PlatformUser } from '@common/schemas/platform-user.schema';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { LivestreamPlatform } from '@common/constants';

@Injectable()
export class LiveManagerService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(LiveSession.name)
    private liveSessionModel: Model<LiveSession>,
    @InjectModel(PlatformUser.name)
    private platformUserModel: Model<PlatformUser>,
    private readonly userService: PlatformUserService,
  ) {
    this.logger.setContext(LiveManagerService.name);
  }

  async getLiveSessionByUsername(username: string): Promise<LiveSession[]> {
    const liveSessions = await this.liveSessionModel
      .find({ username })
      .select('-comments')
      .exec();
    return liveSessions.map(
      session =>
        ({
          id: session.id,
          roomId: session.roomId,
          roomTitle: session.roomTitle,
          deviceId: session.deviceId,
          username: session.username,
          createdAt: session.createdAt,
        } as LiveSession),
    );
  }

  async createLiveSession(
    livestreamPlatform: LivestreamPlatform,
    deviceId: string,
    username: string,
    roomId: string,
    roomTitle: string,
    shareLink: string,
  ): Promise<string> {
    const newLiveSession = new this.liveSessionModel({
      id: uuidv4(),
      roomId: roomId,
      deviceId,
      username,
      roomTitle,
      shareLink,
      livestreamPlatform,
    });
    await newLiveSession.save();
    return newLiveSession.id;
  }
}
