import { LoggerModule } from '@common/logger/logger.module';
import { AppLogger } from '@common/logger/logger.service';
import { Module } from '@nestjs/common';
import { LiveManagerModule } from '@module/live-manager/live-manager.module';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import { LiveCommentService } from './services/live-comment.service';
import { LiveCommentFactory } from './services/live-comment.factory';
import { TiktokLiveCommentService } from './services/tiktok-comment.service';
import { YoutubeLiveCommentService } from './services/youtube-comment.service';
import { SystemUser, SystemUserSchema } from '@common/schemas';
import { MongooseModule } from '@nestjs/mongoose';
import { WsPermissionsGuard } from '@common/guards/ws-permission.guard';
import { JwtService } from '@nestjs/jwt';
import { APP_FILTER } from '@nestjs/core';
import { WebSocketExceptionFilter } from '@common/filters/websocket.filter';
import { SchemaCollectionName } from '@common/constants/schema';
import { PlatformChannelModule } from '@module/platform-channel/platform-channel.module';
import { SystemConfigModule } from '@module/system-config/system-config.module';
import { LiveSessionModule } from '@module/live-session/live-session.module';
import { ScheduleModule } from '@nestjs/schedule';
import { RedisModule } from '@shared/redis';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LiveSessionTrackingModule } from './live-session-tracking.module';

@Module({
  imports: [
    LoggerModule,
    LiveManagerModule,
    PlatformUserModule,
    PlatformChannelModule,
    SystemConfigModule,
    LiveSessionTrackingModule,
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
    LiveSessionModule,
    RedisModule.forRootAsync({
      imports: [ConfigModule, LoggerModule],
      inject: [ConfigService],
    }),
  ],
  providers: [
    LiveCommentService,
    LiveCommentFactory,
    TiktokLiveCommentService,
    YoutubeLiveCommentService,

    AppLogger,
    WsPermissionsGuard,
    JwtService,
    {
      provide: APP_FILTER,
      useClass: WebSocketExceptionFilter,
    },
  ],
  exports: [LiveCommentService],
})
export class LiveCommentModule {}
