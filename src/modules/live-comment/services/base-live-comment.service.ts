import { Socket } from 'socket.io';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { LivestreamPlatform } from '@common/constants';
import { LiveSessionRedisService } from '../redis/live-session.redis';
import { AppLogger } from '@common/logger/logger.service';

export abstract class BaseLiveCommentService {
  protected platformChannelId: string;
  protected sessionId: string;

  constructor(
    protected readonly liveSessionService: LiveSessionService,
    protected readonly liveSessionRedisService: LiveSessionRedisService,
    protected readonly logger: AppLogger,
  ) {
    this.logger.setContext(this.constructor.name);
  }

  abstract createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platformChannelId: string,
    sessionId: string,
  ): Promise<void>;

  setSessionId(sessionId: string) {
    this.sessionId = sessionId;
  }

  getSessionId() {
    return this.sessionId;
  }

  /**
   * Map a disconnect callback for a socket client and platform channel
   */
  protected async mapDisconnectCallback(
    socketClientId: string,
    platformChannelId: string,
    disconnectCallback: () => void,
  ): Promise<void> {
    try {
      // Get the sessionId from the socket client
      const sessionIds =
        await this.liveSessionRedisService.getSessionsBySocketId(
          socketClientId,
        );

      // Find the session with matching platformChannelId
      for (const sessionId of sessionIds) {
        const sessionData = await this.liveSessionRedisService.get(sessionId);
        if (
          sessionData &&
          sessionData.platformChannelId === platformChannelId
        ) {
          // Map disconnect callback using sessionId
          this.liveSessionRedisService.mapDisconnect(
            sessionId,
            platformChannelId,
            disconnectCallback,
          );
          break;
        }
      }
    } catch (error) {
      this.logger.error(
        `Error mapping disconnect callback: ${error.message}`,
        error.stack,
      );
    }
  }

  async emitToClient(
    socket: Socket,
    commentId: string,
    eventName: string,
    liveId: string,
    data: any,
    platform: LivestreamPlatform,
  ): Promise<void> {
    try {
      // Check if this comment has already been sent to this session
      if (
        await this.liveSessionRedisService.hasSentComment(
          this.sessionId,
          commentId,
        )
      ) {
        return;
      }
    } catch (error) {
      this.logger.error(
        `Error checking sent comment: ${error.message}`,
        error.stack,
      );
    }

    socket.emit(eventName, data);

    if (platform === LivestreamPlatform.FACEBOOK) {
      socket.emit(`${eventName}-${socket.id}`, data);
    } else {
      socket.emit(`${eventName}-${socket.id}-${liveId}`, data);
      socket.emit(`${eventName}-${socket.id}`, data);
    }

    try {
      // Add the comment to the sent comments for this session
      await this.liveSessionRedisService.addSentComment(
        this.getSessionId(),
        commentId,
      );
    } catch (error) {
      this.logger.error(
        `Error adding sent comment: ${error.message}`,
        error.stack,
      );
    }
  }
}
