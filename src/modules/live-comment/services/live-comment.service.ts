import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import { AppLogger } from '@common/logger/logger.service';
import { LivestreamPlatform } from '@common/constants';
import { LiveCommentFactory } from './live-comment.factory';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { LiveSessionRedisService } from '../redis/live-session.redis';
import { LiveSessionTrackingService } from '../live-session-tracking.service';
import { v4 as uuidv4 } from 'uuid';
import * as AppError from '@common/exceptions/error';

@Injectable()
export class LiveCommentService {
  constructor(
    private readonly logger: AppLogger,
    private readonly liveCommentFactory: LiveCommentFactory,
    private readonly liveSessionTrackingService: LiveSessionTrackingService,
    private readonly liveSessionRedisService: LiveSessionRedisService,
  ) {
    this.logger.setContext(LiveCommentService.name);
  }

  async createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platform: LivestreamPlatform = LivestreamPlatform.TIKTOK,
    platformChannelId: string,
  ): Promise<void> {
    // Check if user can start a new session based on subscription limits
    const canStartNewSession =
      await this.liveSessionTrackingService.canStartNewSession(userId);
    if (!canStartNewSession) {
      socketClient.emit('live_error', {
        errorCode: AppError.TRIAL_DAYS_ENDED.errorCode,
        message: AppError.TRIAL_DAYS_ENDED.errorMessage,
      });
      return;
    }

    // Check if user has streaming minutes available
    const canStreamMoreMinutes =
      await this.liveSessionTrackingService.canStreamMoreMinutes(userId, 0);
    if (!canStreamMoreMinutes) {
      this.logger.log(`User ${userId} has reached their streaming limit`);
      socketClient.emit('live_error', {
        errorCode: AppError.STREAMING_LIMIT_REACHED.errorCode,
        message: AppError.STREAMING_LIMIT_REACHED.errorMessage,
      });
      return;
    }

    const liveService = this.liveCommentFactory.createLiveService(platform);

    if (!liveService) {
      this.logger.error(`Init live service with platform ${platform} error`);
      return;
    }

    // Generate a unique session ID for tracking
    const sessionId = uuidv4();

    // Start tracking this session
    await this.liveSessionTrackingService.startStreamTracking(
      userId,
      sessionId,
      socketClient,
    );

    // Store user info in Redis for later use (e.g., when disconnecting)
    await this.liveSessionRedisService.set(sessionId, {
      socketClientId: socketClient.id,
      user: { id: userId, username },
      platformChannelId,
    });

    // Set up a heartbeat interval to keep the session alive
    const heartbeatInterval = setInterval(async () => {
      try {
        await this.liveSessionTrackingService.updateHeartbeat(
          userId,
          sessionId,
        );
      } catch (error) {
        this.logger.error(`Error updating heartbeat: ${error.message}`);
      }
    }, 30000); // Every 30 seconds

    // Initialize heartbeatIntervals if it doesn't exist
    if (!socketClient.data.heartbeatIntervals) {
      socketClient.data.heartbeatIntervals = {};
    }

    // Store the interval for cleanup, indexed by sessionId
    socketClient.data.heartbeatIntervals[sessionId] = heartbeatInterval;

    // Clean up on disconnect
    socketClient.once('disconnect', () => {
      if (
        socketClient.data.heartbeatIntervals &&
        socketClient.data.heartbeatIntervals[sessionId]
      ) {
        clearInterval(socketClient.data.heartbeatIntervals[sessionId]);
        delete socketClient.data.heartbeatIntervals[sessionId];
      }
    });

    liveService.createLive(
      socketClient,
      username,
      userId,
      saveHistory,
      liveId,
      platformChannelId,
      sessionId,
    );
  }

  async stopLive(
    socketClient: Socket,
    platformChannelId: string,
    sessionId?: string,
  ): Promise<void> {
    try {
      // If sessionId is provided, use it directly
      if (sessionId) {
        // Get session data from Redis
        const sessionData = await this.liveSessionRedisService.get(sessionId);

        // End tracking if session exists
        if (sessionData && sessionData.user) {
          await this.liveSessionTrackingService.endStreamTracking(
            sessionData.user.id,
            sessionId,
          );
        }

        // Clear heartbeat interval if it exists
        if (
          socketClient.data.heartbeatIntervals &&
          socketClient.data.heartbeatIntervals[sessionId]
        ) {
          clearInterval(socketClient.data.heartbeatIntervals[sessionId]);
          delete socketClient.data.heartbeatIntervals[sessionId];
        }

        // Delete session from Redis
        await this.liveSessionRedisService.deleteSession(
          sessionId,
          platformChannelId,
        );
      } else {
        // Get all sessions for this socket
        const sessionIds =
          await this.liveSessionRedisService.getSessionsBySocketId(
            socketClient.id,
          );

        // Find the session with matching platformChannelId
        for (const sid of sessionIds) {
          const sessionData = await this.liveSessionRedisService.get(sid);
          if (
            sessionData &&
            sessionData.platformChannelId === platformChannelId
          ) {
            // End tracking if session exists
            if (sessionData.user) {
              await this.liveSessionTrackingService.endStreamTracking(
                sessionData.user.id,
                sid,
              );
            }

            // Clear heartbeat interval if it exists
            if (
              socketClient.data.heartbeatIntervals &&
              socketClient.data.heartbeatIntervals[sid]
            ) {
              clearInterval(socketClient.data.heartbeatIntervals[sid]);
              delete socketClient.data.heartbeatIntervals[sid];
            }

            // Delete session from Redis
            await this.liveSessionRedisService.deleteSession(
              sid,
              platformChannelId,
            );
            break;
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Error stopping live session: ${error.message}`,
        error.stack,
      );
    }
  }
}
