import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PaginationService } from '@shared/services/pagination.service';
import {
  LiveSessionGroup,
  LiveSessionGroupDocument,
} from '@common/schemas/live-session-group.schema';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';
import { LiveSessionGroupMapper } from '@module/live-session-group/live-session-group.mapper';
import { UpsertLiveSessionGroupDto } from '@module/live-session-group/dtos/upsert-live-session-group.dto';
import { LiveSession, LiveSessionDocument } from '@common/schemas';

@Injectable()
export class LiveSessionGroupService {
  constructor(
    private readonly logger: AppLogger,
    private readonly paginationService: PaginationService,
    @InjectModel(LiveSessionGroup.name)
    private liveSessionGroupModel: Model<LiveSessionGroupDocument>,
    @InjectModel(LiveSession.name)
    private liveSessionModel: Model<LiveSessionDocument>,
  ) {
    this.logger.setContext(LiveSessionGroupService.name);
  }

  async create(
    data: UpsertLiveSessionGroupDto,
    createdBy: string,
  ): Promise<LiveSessionGroupDto> {
    const liveSessionGroup = await this.liveSessionGroupModel.create({
      name: data.name,
      createdBy,
    });

    return LiveSessionGroupMapper.toDto(liveSessionGroup);
  }
}
