import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { PaginationService } from '@shared/services/pagination.service';
import {
  LiveSessionGroup,
  LiveSessionGroupDocument,
} from '@common/schemas/live-session-group.schema';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';
import { LiveSessionGroupMapper } from '@module/live-session-group/live-session-group.mapper';
import { UpsertLiveSessionGroupDto } from '@module/live-session-group/dtos/upsert-live-session-group.dto';
import { LiveSession, LiveSessionDocument } from '@common/schemas';
import {
  LiveSessionGroupSession,
  LiveSessionGroupSessionDocument,
} from '@common/schemas/live-session-group-session.schema';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelDocument,
} from '@common/schemas/system-user-platform-channel.schema';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { GetLiveSessionGroupDto } from '@module/live-session-group/dtos/get-live-session-group.dto';
import { LiveSessionGroupDetailsPaginationResultDto } from '@module/live-session-group/dtos/live-session-group-details-pagination.dto';
import * as dayjs from 'dayjs';
import { AppException } from '@common/exceptions/app-exception';
import {
  LIVE_SESSION_GROUP_NOT_EXIST,
  LIVE_SESSION_GROUP_ACCESS_FORBIDDEN,
  FORBIDDEN,
  LIVE_SESSION_NOT_EXIST,
} from '@common/exceptions/error';

@Injectable()
export class LiveSessionGroupService {
  constructor(
    private readonly logger: AppLogger,
    private readonly paginationService: PaginationService,
    @InjectModel(LiveSessionGroup.name)
    private liveSessionGroupModel: Model<LiveSessionGroupDocument>,
    @InjectModel(LiveSession.name)
    private liveSessionModel: Model<LiveSessionDocument>,
    @InjectModel(LiveSessionGroupSession.name)
    private liveSessionGroupSessionModel: Model<LiveSessionGroupSessionDocument>,
    @InjectModel(SystemUserPlatformChannel.name)
    private systemUserPlatformChannelModel: Model<SystemUserPlatformChannelDocument>,
  ) {
    this.logger.setContext(LiveSessionGroupService.name);
  }

  async create(
    data: UpsertLiveSessionGroupDto,
    createdBy: string,
  ): Promise<LiveSessionGroupDto> {
    const liveSessionGroup = await this.liveSessionGroupModel.create({
      name: data.name,
      createdBy,
    });

    return LiveSessionGroupMapper.toDto(liveSessionGroup);
  }

  async getMyLiveSessionGroups(
    userId: string,
    options: PaginationOptionsDto,
    filters: GetLiveSessionGroupDto,
  ): Promise<LiveSessionGroupDetailsPaginationResultDto> {
    // Build filter query for live session groups
    const filterQuery: FilterQuery<LiveSessionGroupDocument> = {};

    if (filters.search) {
      filterQuery.name = { $regex: filters.search, $options: 'i' };
    }

    // Get paginated live session groups
    const result = await this.paginationService.paginate(
      this.liveSessionGroupModel,
      filterQuery,
      options,
    );

    // Get live session counts for each group
    const groupsWithCounts = await Promise.all(
      result.data.map(async group => {
        const sessionCount =
          await this.liveSessionGroupSessionModel.countDocuments({
            liveSessionGroupId: group.id,
          });

        return LiveSessionGroupMapper.toDetailsDto(group, sessionCount);
      }),
    );

    return {
      data: groupsWithCounts,
      meta: result.meta,
    };
  }

  /**
   * Find or create a live session group for today's date and a specific user
   * @param userId The user ID
   * @returns LiveSessionGroupDto
   */
  async findOrCreateTodayGroup(userId: string): Promise<LiveSessionGroupDto> {
    const today = dayjs().format('DD-MM-YYYY');
    const groupName = `Livestream ngày ${today}`;

    // Get start and end of today for date range query
    const startOfDay = dayjs().startOf('day').toDate();
    const endOfDay = dayjs().endOf('day').toDate();

    // Use findOneAndUpdate with upsert to handle parallel processes safely
    const liveSessionGroup = await this.liveSessionGroupModel.findOneAndUpdate(
      {
        createdBy: userId,
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay,
        },
      },
      {
        $setOnInsert: {
          name: groupName,
          createdBy: userId,
        },
      },
      {
        upsert: true,
        new: true,
      },
    );

    return LiveSessionGroupMapper.toDto(liveSessionGroup);
  }

  /**
   * Update an existing live session group's properties
   * @param groupId The group ID to update
   * @param updateData The data to update
   * @param userId The user ID for authorization
   * @returns LiveSessionGroupDto
   */
  async update(
    groupId: string,
    updateData: UpsertLiveSessionGroupDto,
    userId: string,
  ): Promise<LiveSessionGroupDto> {
    // Find the group and check if it exists and user has permission
    const existingGroup = await this.liveSessionGroupModel.findOne({
      id: groupId,
    });

    if (!existingGroup) {
      throw new AppException(LIVE_SESSION_GROUP_NOT_EXIST);
    }

    if (existingGroup.createdBy !== userId) {
      throw new AppException(LIVE_SESSION_GROUP_ACCESS_FORBIDDEN);
    }

    // Update the group
    const updatedGroup = await this.liveSessionGroupModel.findOneAndUpdate(
      { id: groupId },
      {
        $set: {
          name: updateData.name,
        },
      },
      { new: true },
    );

    return LiveSessionGroupMapper.toDto(updatedGroup);
  }

  /**
   * Update an existing live session group's properties
   * @param groupId The group ID to update
   * @param userId The user ID for authorization
   * @returns LiveSessionGroupDto
   */
  async findOne(groupId: string, userId: string): Promise<LiveSessionGroupDto> {
    const existingGroup = await this.liveSessionGroupModel.findOne({
      id: groupId,
    });

    if (!existingGroup) {
      throw new AppException(LIVE_SESSION_GROUP_NOT_EXIST);
    }

    if (existingGroup.createdBy !== userId) {
      throw new AppException(LIVE_SESSION_GROUP_ACCESS_FORBIDDEN);
    }

    return LiveSessionGroupMapper.toDto(existingGroup);
  }

  /**
   * Delete a live session group and its associated junction table records
   * @param groupId The group ID to delete
   * @param userId The user ID for authorization
   */
  async delete(groupId: string, userId: string): Promise<void> {
    // Find the group and check if it exists and user has permission
    const existingGroup = await this.liveSessionGroupModel.findOne({
      id: groupId,
    });

    if (!existingGroup) {
      throw new AppException(LIVE_SESSION_GROUP_NOT_EXIST);
    }

    if (existingGroup.createdBy !== userId) {
      throw new AppException(LIVE_SESSION_GROUP_ACCESS_FORBIDDEN);
    }

    // Delete related junction table records first
    await this.liveSessionGroupSessionModel.deleteMany({
      liveSessionGroupId: groupId,
    });

    // Delete the group
    await this.liveSessionGroupModel.deleteOne({ id: groupId });
  }

  /**
   * Add a live session to a live session group using the junction table
   * @param liveSessionId The live session ID to add
   * @param liveSessionGroupId The live session group ID to add to
   * @param userId The user ID for authorization
   * @returns LiveSessionGroupSessionDocument
   */
  async addLiveSessionToGroup(
    liveSessionId: string,
    liveSessionGroupId: string,
    userId: string,
  ): Promise<LiveSessionGroupSessionDocument> {
    // 1. Verify the live session group exists and user is the creator
    const liveSessionGroup = await this.liveSessionGroupModel.findOne({
      id: liveSessionGroupId,
    });

    if (!liveSessionGroup) {
      throw new AppException(LIVE_SESSION_GROUP_NOT_EXIST);
    }

    if (liveSessionGroup.createdBy !== userId) {
      throw new AppException(LIVE_SESSION_GROUP_ACCESS_FORBIDDEN);
    }

    // 2. Verify the live session exists
    const liveSession = await this.liveSessionModel.findOne({
      id: liveSessionId,
    });

    if (!liveSession) {
      throw new AppException(LIVE_SESSION_NOT_EXIST);
    }

    // 3. Verify user has access to the live session through platform channels
    const systemUserPlatformChannel =
      await this.systemUserPlatformChannelModel.find({
        systemUser: userId,
        platformChannel: liveSession.platformChannelId,
      });

    if (!systemUserPlatformChannel) {
      throw new AppException(FORBIDDEN);
    }

    // 4. Create or return existing junction table record (idempotent)
    const junctionRecord =
      await this.liveSessionGroupSessionModel.findOneAndUpdate(
        {
          liveSessionGroupId,
          liveSessionId,
        },
        {
          $setOnInsert: {
            liveSessionGroupId,
            liveSessionId,
          },
        },
        {
          upsert: true,
          new: true,
        },
      );

    return junctionRecord;
  }
}
