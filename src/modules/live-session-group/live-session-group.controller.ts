import { Body, Controller, Delete, Param, Patch, Post } from '@nestjs/common';
import { ApiOkResponse, ApiTags, ApiNoContentResponse } from '@nestjs/swagger';
import { LiveSessionGroupService } from '@module/live-session-group/live-session-group.service';
import { Auth, CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';
import { UpsertLiveSessionGroupDto } from '@module/live-session-group/dtos/upsert-live-session-group.dto';

@ApiTags('LiveSessionGroup')
@Controller('/live-session-groups')
export class LiveSessionGroupController {
  constructor(
    private readonly liveSessionGroupService: LiveSessionGroupService,
  ) {}

  @Auth()
  @Post('/')
  @ApiOkResponse({ type: LiveSessionGroupDto })
  create(
    @Body() data: UpsertLiveSessionGroupDto,
    @CurrentUser() user: UserPayload,
  ): Promise<LiveSessionGroupDto> {
    return this.liveSessionGroupService.create(data, user.id);
  }

  @Auth()
  @Patch('/:id')
  @ApiOkResponse({ type: LiveSessionGroupDto })
  update(
    @Param('id') groupId: string,
    @Body() data: UpsertLiveSessionGroupDto,
    @CurrentUser() user: UserPayload,
  ): Promise<LiveSessionGroupDto> {
    return this.liveSessionGroupService.update(groupId, data, user.id);
  }

  @Auth()
  @Delete('/:id')
  @ApiNoContentResponse()
  delete(
    @Param('id') groupId: string,
    @CurrentUser() user: UserPayload,
  ): Promise<void> {
    return this.liveSessionGroupService.delete(groupId, user.id);
  }
}
