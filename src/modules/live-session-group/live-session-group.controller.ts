import { Body, Controller, Post } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { LiveSessionGroupService } from '@module/live-session-group/live-session-group.service';
import { Auth, CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';
import { UpsertLiveSessionGroupDto } from '@module/live-session-group/dtos/upsert-live-session-group.dto';

@ApiTags('LiveSessionGroup')
@Controller('/live-session-groups')
export class LiveSessionGroupController {
  constructor(
    private readonly liveSessionGroupService: LiveSessionGroupService,
  ) {}

  @Auth()
  @Post('/')
  @ApiOkResponse({ type: LiveSessionGroupDto })
  create(
    @Body() data: UpsertLiveSessionGroupDto,
    @CurrentUser() user: UserPayload,
  ): Promise<LiveSessionGroupDto> {
    return this.liveSessionGroupService.create(data, user.id);
  }
}
