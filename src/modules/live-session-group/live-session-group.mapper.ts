import { LiveSessionGroupDocument } from '@common/schemas/live-session-group.schema';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';
import { LiveSessionGroupDetailsDto } from '@module/live-session-group/dtos/live-session-group-details.dto';

export class LiveSessionGroupMapper {
  static toDto(entity: LiveSessionGroupDocument): LiveSessionGroupDto {
    return {
      id: entity.id,
      name: entity.name,
      createdAt: entity.createdAt,
    };
  }

  static toDetailsDto(
    entity: LiveSessionGroupDocument,
    totalLiveSessions: number,
  ): LiveSessionGroupDetailsDto {
    return {
      ...this.toDto(entity),
      totalLiveSessions,
    };
  }
}
