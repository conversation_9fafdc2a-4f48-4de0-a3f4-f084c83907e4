import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { LiveSessionGroupDetailsDto } from '@module/live-session-group/dtos/live-session-group-details.dto';

export class LiveSessionGroupDetailsPaginationResultDto extends PaginationResultDto<LiveSessionGroupDetailsDto> {
  @ApiProperty({ type: () => [LiveSessionGroupDetailsDto] })
  data: LiveSessionGroupDetailsDto[];

  @ApiProperty({ type: () => PaginationMetaDto })
  meta: PaginationMetaDto;
}
