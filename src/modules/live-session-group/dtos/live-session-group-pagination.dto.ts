import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { LiveSessionGroupDto } from '@module/live-session-group/dtos/live-session-group.dto';

export class LiveSessionGroupPaginationResultDto extends PaginationResultDto<LiveSessionGroupDto> {
  @ApiProperty({ type: () => [LiveSessionGroupDto] })
  data: LiveSessionGroupDto[];

  @ApiProperty({ type: () => PaginationMetaDto })
  meta: PaginationMetaDto;
}
