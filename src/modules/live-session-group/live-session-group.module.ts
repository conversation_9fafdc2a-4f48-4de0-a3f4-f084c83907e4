import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { LiveSessionGroupService } from '@module/live-session-group/live-session-group.service';
import { LiveSessionGroupController } from '@module/live-session-group/live-session-group.controller';
import {
  LiveSessionGroup,
  LiveSessionGroupSchema,
} from '@common/schemas/live-session-group.schema';

@Module({
  imports: [
    LoggerModule,
    SharedModule,
    MongooseModule.forFeature([
      {
        name: LiveSessionGroup.name,
        schema: LiveSessionGroupSchema,
        collection: SchemaCollectionName.LiveSessionGroup,
      },
    ]),
  ],
  controllers: [LiveSessionGroupController],
  providers: [LiveSessionGroupService],
  exports: [LiveSessionGroupService],
})
export class LiveSessionGroupModule {}
