import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { LiveSessionGroupService } from '@module/live-session-group/live-session-group.service';
import { LiveSessionGroupController } from '@module/live-session-group/live-session-group.controller';
import {
  LiveSessionGroup,
  LiveSessionGroupSchema,
} from '@common/schemas/live-session-group.schema';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelSchema,
} from '@common/schemas/system-user-platform-channel.schema';
import { LiveSession, LiveSessionSchema } from '@common/schemas';

@Module({
  imports: [
    LoggerModule,
    SharedModule,
    MongooseModule.forFeature([
      {
        name: LiveSessionGroup.name,
        schema: LiveSessionGroupSchema,
        collection: SchemaCollectionName.LiveSessionGroup,
      },

      {
        name: LiveSession.name,
        schema: LiveSessionSchema,
        collection: SchemaCollectionName.LiveSession,
      },
      {
        name: SystemUserPlatformChannel.name,
        schema: SystemUserPlatformChannelSchema,
        collection: SchemaCollectionName.SystemUserPlatformChannel,
      },
    ]),
  ],
  controllers: [LiveSessionGroupController],
  providers: [LiveSessionGroupService],
  exports: [LiveSessionGroupService],
})
export class LiveSessionGroupModule {}
