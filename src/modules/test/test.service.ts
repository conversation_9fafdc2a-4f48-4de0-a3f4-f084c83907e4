import { Injectable } from '@nestjs/common';
import { AppLogger } from '@common/logger/logger.service';
import { InjectConnection, InjectModel, Prop } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import {
  PlatformChannel,
  PlatformChannelDocument,
} from '@common/schemas/platform-channel.schema';
import {
  LiveSession,
  LiveSessionDocument,
  PlatformUser,
  PlatformUserDocument,
  Subscription,
  SubscriptionDocument,
  SubscriptionPlan,
  SystemUser,
  SystemUserDocument,
} from '@common/schemas';
import { MigrationResponseDto } from '@common/dtos/migration-response.dto';
import { v4 as uuidv4 } from 'uuid';
import { Comment, CommentDocument } from '@common/schemas/comment.schema';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { CommentService } from '@module/comment/comment.service';
import { LivestreamPlatform } from '@common/constants';
import { YoutubeCommentMapper } from '@module/live-comment/mappers/youtube-comment.mapper';
import { chunk } from 'lodash';
import * as dayjs from 'dayjs';

@Injectable()
export class TestService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(PlatformChannel.name)
    private readonly platformChannelModel: Model<PlatformChannelDocument>,
    @InjectModel(PlatformUser.name)
    private readonly platformUserModel: Model<PlatformUserDocument>,
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUserDocument>,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    @InjectModel(LiveSession.name)
    private readonly liveSessionModel: Model<LiveSessionDocument>,
    @InjectConnection() private readonly connection: mongoose.Connection,
    protected readonly platformUserService: PlatformUserService,
    protected readonly commentService: CommentService,
  ) {
    this.logger.setContext(TestService.name);
  }

  getStatus() {
    this.logger.log('Test status endpoint called');
    return {
      status: 'OK',
      timestamp: new Date().toISOString(),
      message: 'Service is running correctly',
    };
  }

  async migrate() {
    const superadmin = await this.systemUserModel.findOne({
      username: 'superadmin',
    });

    await this.platformChannelModel.updateMany(
      {
        createdBy: null,
      },
      {
        createdBy: superadmin.id,
      },
      { new: true },
    );

    return 'OK';
  }

  /**
   * Migrate all users without a subscription to the free subscription plan
   */
  async migrateSubscriptions(): Promise<MigrationResponseDto> {
    this.logger.log('Starting subscription migration');

    // Initialize response
    const response: MigrationResponseDto = {
      success: false,
      message: '',
      migratedCount: 0,
      migratedIds: [], // Using the standardized field name
      totalProcessed: 0,
      skippedCount: 0,
      errors: [],
    };

    try {
      // Step 1: Find the free subscription plan
      const freeSubscription = await this.subscriptionModel
        .findOne({
          plan: SubscriptionPlan.FREE,
        })
        .exec();

      if (!freeSubscription) {
        response.message =
          'Free subscription plan not found. Migration aborted.';
        this.logger.warn(response.message);
        return response;
      }

      this.logger.log(
        `Found free subscription plan: ${freeSubscription.name} (${freeSubscription.id})`,
      );

      // Step 2: Find all users without a subscription
      const usersWithoutSubscription = await this.systemUserModel
        .find({
          $or: [
            { subscriptionId: { $exists: false } },
            { subscriptionId: null },
          ],
        })
        .exec();

      response.totalProcessed = usersWithoutSubscription.length;
      this.logger.log(
        `Found ${response.totalProcessed} users without a subscription`,
      );

      if (response.totalProcessed === 0) {
        response.success = true;
        response.message =
          'No users found without a subscription. Nothing to migrate.';
        return response;
      }

      // Step 3: Assign the free subscription to each user
      for (const user of usersWithoutSubscription) {
        try {
          // Update the user with the free subscription
          await this.systemUserModel
            .updateOne(
              { _id: user._id },
              { subscriptionId: freeSubscription.id },
            )
            .exec();

          response.migratedCount++;
          response.migratedIds.push(user.id);
          this.logger.log(
            `Assigned free subscription to user: ${user.username} (${user.id})`,
          );
        } catch (error) {
          const errorMessage = `Failed to assign subscription to user ${user.id}: ${error.message}`;
          response.errors.push(errorMessage);
          this.logger.error(errorMessage, error.stack);
        }
      }

      response.skippedCount = response.totalProcessed - response.migratedCount;
      response.success = response.errors.length === 0;
      response.message = `Successfully migrated ${response.migratedCount} users to the free subscription plan.`;

      if (response.skippedCount > 0) {
        response.message += ` Skipped ${response.skippedCount} users due to errors.`;
      }

      this.logger.log(`Subscription migration completed: ${response.message}`);
      return response;
    } catch (error) {
      const errorMessage = `Subscription migration failed: ${error.message}`;
      response.success = false;
      response.message = errorMessage;
      response.errors.push(errorMessage);
      this.logger.error(errorMessage, error.stack);
      return response;
    }
  }

  /**
   * Migrate all SystemUser records to update timestamp fields
   * Updates createdAt, updatedAt, and activeAt fields to current timestamp
   */
  async migrateSystemUsers(): Promise<MigrationResponseDto> {
    this.logger.log('Starting SystemUser timestamp migration');

    // Initialize response
    const response: MigrationResponseDto = {
      success: false,
      message: '',
      migratedCount: 0,
      migratedIds: [],
      totalProcessed: 0,
      skippedCount: 0,
      errors: [],
    };

    try {
      // Step 1: Find all SystemUser records
      const allUsers = await this.systemUserModel.find({}).exec();

      response.totalProcessed = allUsers.length;
      this.logger.log(
        `Found ${response.totalProcessed} SystemUser records to migrate`,
      );

      if (response.totalProcessed === 0) {
        response.success = true;
        response.message = 'No SystemUser records found. Nothing to migrate.';
        return response;
      }

      // Step 2: Update each user with current timestamp
      const currentTimestamp = new Date();

      for (const user of allUsers) {
        try {
          // Update the user with current timestamp for all three fields
          await this.systemUserModel
            .updateOne(
              { _id: user._id },
              {
                createdAt: currentTimestamp,
                updatedAt: currentTimestamp,
                activeAt: currentTimestamp,
              },
            )
            .exec();

          response.migratedCount++;
          response.migratedIds.push(user.id);
          this.logger.log(
            `Updated timestamps for user: ${user.username} (${user.id})`,
          );
        } catch (error) {
          const errorMessage = `Failed to update timestamps for user ${user.id}: ${error.message}`;
          response.errors.push(errorMessage);
          this.logger.error(errorMessage, error.stack);
        }
      }

      response.skippedCount = response.totalProcessed - response.migratedCount;
      response.success = response.errors.length === 0;
      response.message = `Successfully updated timestamps for ${response.migratedCount} SystemUser records.`;

      if (response.skippedCount > 0) {
        response.message += ` Skipped ${response.skippedCount} users due to errors.`;
      }

      this.logger.log(
        `SystemUser timestamp migration completed: ${response.message}`,
      );
      return response;
    } catch (error) {
      const errorMessage = `SystemUser timestamp migration failed: ${error.message}`;
      response.success = false;
      response.message = errorMessage;
      response.errors.push(errorMessage);
      this.logger.error(errorMessage, error.stack);
      return response;
    }
  }

  /**
   * Migrate all PlatformUser records to update id (uuid) fields
   */
  async migratePlatformUsers(): Promise<MigrationResponseDto> {
    this.logger.log('Starting PlatformUser ID migration');

    // Initialize response
    const response: MigrationResponseDto = {
      success: false,
      message: '',
      migratedCount: 0,
      migratedIds: [],
      totalProcessed: 0,
      skippedCount: 0,
      errors: [],
    };

    try {
      // Step 1: Find all PlatformUser records
      const allUsers = await this.platformUserModel
        .find({
          $or: [{ id: { $exists: false } }, { id: null }],
        })
        .exec();

      response.totalProcessed = allUsers.length;
      this.logger.log(
        `Found ${response.totalProcessed} PlatformUser records to migrate`,
      );

      if (response.totalProcessed === 0) {
        response.success = true;
        response.message = 'No PlatformUser records found. Nothing to migrate.';
        return response;
      }

      // Step 2: Update each user with a new UUID
      for (const user of allUsers) {
        try {
          // Generate a new UUID for the user
          const newId = uuidv4();

          // Update the user with the new UUID
          await this.platformUserModel
            .updateOne({ _id: user._id }, { id: newId })
            .exec();

          response.migratedCount++;
          response.migratedIds.push(newId);
          this.logger.log(`Updated ID for user: ${user.userId} (${user.id})`);
        } catch (error) {
          const errorMessage = `Failed to update ID for user ${user.id}: ${error.message}`;
          response.errors.push(errorMessage);
          this.logger.error(errorMessage, error.stack);
        }
      }

      response.skippedCount = response.totalProcessed - response.migratedCount;
      response.success = response.errors.length === 0;
      response.message = `Successfully updated IDs for ${response.migratedCount} PlatformUser records.`;

      if (response.skippedCount > 0) {
        response.message += ` Skipped ${response.skippedCount} users due to errors.`;
      }

      this.logger.log(
        `PlatformUser ID migration completed: ${response.message}`,
      );
      return response;
    } catch (error) {
      const errorMessage = `PlatformUser ID migration failed: ${error.message}`;
      response.success = false;
      response.message = errorMessage;
      response.errors.push(errorMessage);
      this.logger.error(errorMessage, error.stack);
      return response;
    }
  }

  migrateLiveSessions(): any {
    (async () => {
      const platformChannels = await this.platformChannelModel
        .find({
          livestreamPlatform: {
            $in: [LivestreamPlatform.TIKTOK, LivestreamPlatform.YOUTUBE],
          },
        })
        .exec();

      for (const channel of platformChannels) {
        console.log('Migrating live session for channel:', channel.id);

        const liveSessions = await this.liveSessionModel
          .find({
            username: channel.channelId,
            livestreamPlatform: channel.livestreamPlatform,
          })
          .exec();

        for (const session of liveSessions) {
          if (!session.platformChannelId) {
            session.platformChannelId = channel.id;

            if (!session.roomTitle) {
              session.roomTitle = `Livestream ${
                session?.createdAt
                  ? `ngày ${dayjs(session.createdAt).format('DD/MM/YYYY')}`
                  : ''
              } kênh ${channel.name}`.replace('  ', ' ');
            }

            await session.save();
          }
          console.log(
            `Live session ${session.id} for channel ${channel.id} migrated successfully`,
          );
        }
      }

      this.logger.log('Live session migration completed successfully');
    })();
    return 1;
  }

  migrateComments(): any {
    // (async () => {
    //   const result = await this.connection.db
    //     .collection('live_sessions')
    //     .find({
    //       id: {
    //         $exists: true,
    //         $ne: null,
    //       },
    //       comments: {
    //         $exists: true,
    //         $ne: [],
    //       },
    //     })
    //     .toArray();
    //
    //   const processes: any[] = [];
    //
    //   result.forEach(session => {
    //     session?.comments.forEach(comment => {
    //       if (comment?.user) {
    //         let commentText = comment.comment;
    //
    //         if (session.livestreamPlatform === LivestreamPlatform.YOUTUBE) {
    //           const youtubeCommentMapper = new YoutubeCommentMapper();
    //           try {
    //             const parsedComment = JSON.parse(comment.comment);
    //
    //             commentText =
    //               youtubeCommentMapper.fromYTCommentToText(parsedComment);
    //           } catch {
    //             commentText = comment.comment;
    //           }
    //         }
    //
    //         processes.push(async () => {
    //           const platformUser = await this.platformUserService.upsertUser({
    //             userId: comment?.user?.userId,
    //             secUid: comment?.user?.secUid,
    //             uniqueId: comment?.user?.uniqueId,
    //             nickname: comment?.user?.nickname,
    //             livestreamPlatform: comment?.user?.livestreamPlatform,
    //             profilePictureUrl: comment?.user?.profilePictureUrl,
    //           });
    //           await this.commentService.addCommentToLive({
    //             commentId: comment.msgId,
    //             liveSessionId: session.id,
    //             roomId: session.roomId,
    //             message: commentText,
    //             platformUserId: platformUser.id,
    //             liveSessionGroupId
    //           });
    //         });
    //       }
    //     });
    //   });
    //
    //   const chunked = chunk(processes, 200);
    //
    //   for (const chunkedProcesses of chunked) {
    //     this.logger.log(
    //       `Processing chunk of ${chunkedProcesses.length} comments`,
    //     );
    //     await Promise.all(chunkedProcesses.map(fn => fn()));
    //   }
    //
    //   this.logger.log(
    //     `Successfully migrated ${processes.length} comments to live sessions`,
    //   );
    // })();
    //
    // return 1;
  }
}
